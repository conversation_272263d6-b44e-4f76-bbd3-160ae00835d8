.awesome-reports-table {
    width: 100% !important;
    max-width: none !important;
}

.awesome-reports-table th,
.awesome-reports-table td {
    padding: 12px 8px !important;
}

.awesome-reports-card {
    margin-bottom: 20px;
    max-width: 100%;
}

.awesome-reports-card .card {
    max-width: none !important;
}

.awesome-reports-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    margin-bottom: 30px;
}

.awesome-reports-update-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    padding: 12px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.awesome-reports-update-card h3 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    margin: -12px -12px 12px -12px;
    background: #f6f7f7;
    border-bottom: 1px solid #e1e1e1;
}

.awesome-reports-update-card .dashicons {
    width: 24px;
    height: 24px;
    font-size: 24px;
    color: #646970;
}

.awesome-reports-update-card .count {
    font-size: 48px;
    font-weight: bold;
    color: #1d2327;
    margin: 10px 0 5px 0;
    line-height: 1;
    display: flex;
    align-items: baseline;
    gap: 10px;
}

.awesome-reports-update-card .count-comparison {
    font-size: 14px;
    font-weight: 500;
    color: #00a32a;
    display: flex;
    align-items: center;
    gap: 4px;
}

.awesome-reports-update-card .count-comparison .dashicons {
    width: 16px;
    height: 16px;
    font-size: 16px;
    color: #00a32a;
}

.awesome-reports-update-card .updates-list {
    font-size: 13px;
    color: #646970;
    margin: 15px -20px -5px -20px;
}

/* Striped table styling for update items */
.awesome-reports-update-card .update-item {
    margin-bottom: 0;
    padding: 10px 20px;
    border-bottom: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    background: #fff;
}

.awesome-reports-update-card .update-item:nth-child(odd) {
    background: #f9f9f9;
}

.awesome-reports-update-card .update-date {
    font-weight: 400;
    color: #8c8f94;
    flex-shrink: 0;
    min-width: 40px;
}

.awesome-reports-update-card .update-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 600;
    color: #1d2327;
}

.awesome-reports-update-card .update-version {
    color: #2271b1;
    flex-shrink: 0;
    font-weight: 400;
}

@media (max-width: 1200px) {
    .awesome-reports-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 600px) {
    .awesome-reports-grid {
        grid-template-columns: 1fr;
    }

    .awesome-reports-update-card .count {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .awesome-reports-update-card .update-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        padding: 12px 20px;
    }

    .awesome-reports-update-card .update-date {
        min-width: auto;
        order: 2;
    }

    .awesome-reports-update-card .update-name {
        order: 1;
        font-size: 13px;
    }

    .awesome-reports-update-card .update-version {
        order: 3;
        align-self: flex-end;
    }
}
