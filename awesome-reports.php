<?php

/**
 * Plugin Name: Awesome Reports
 * Plugin URI: https://awesomereports.com/
 * Description: Display update statistics directly in the WordPress admin or send reports via email.
 * Version: 1.0.0
 * Author: Bohemia Plugins
 * Author URI: https://bohemiaplugins.com/
 * Text Domain: awesome-reports
 * Domain Path: /languages/
 * 
 * Awesome Reports, 
 * Copyright (C) 2025, Bohemia Plugins, <EMAIL>
 * 
 * Awesome Reports is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * any later version.
 *
 * Awesome Reports is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Awesome Reports. If not, see <https://www.gnu.org/licenses/>.
 * 
 * Awesome Reports incorporates code from WordPress plugin "Awesome Reports"
 * <https://wordpress.org/plugins/awesome-reports/> by <PERSON> <https://mikegillihan.com/>.
 * License: GNU GPL, Version 2
 */

if (! defined('ABSPATH')) {
    exit;
}

define('AWESOME_REPORTS_VERSION', '1.0.0');

/**
 * Create the database table needed for tracking.
 */
function awesome_reports_data_install() {
    global $wpdb;

    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    $charset_collate = $wpdb->get_charset_collate();

    $awesome_reports_sql = "CREATE TABLE $awesome_reports_table_name (
		id mediumint(9) NOT NULL AUTO_INCREMENT,
		date date DEFAULT '0000-00-00' NOT NULL,
        type varchar(191),
        name varchar(191),
        slug varchar(191),
        version_before varchar(191),
        version_after varchar(191),
        active tinyint(1),
		UNIQUE KEY id (id)
	) $charset_collate;";

    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($awesome_reports_sql);

    add_option('awesome_reports_version', AWESOME_REPORTS_VERSION);
}
register_activation_hook(__FILE__, 'awesome_reports_data_install');

/**
 * On plugin activation schedule our daily check for updates.
 */
function awesome_reports_check_for_updates_daily_schedule() {
    // Use wp_next_scheduled to check if the event is already scheduled
    $timestamp = wp_next_scheduled('awesome_reports_check_for_updates_daily');
    // If $timestamp == false schedule daily backups since it hasn't been done previously
    if ($timestamp == false) {
        $timezone = wp_timezone();
        $midnight = new DateTime("00:00:00", $timezone);
        // Schedule the event for right now, then to repeat daily
        wp_schedule_event($midnight->format('U'), 'daily', 'awesome_reports_check_for_updates_daily');
    }
}
register_activation_hook(__FILE__, 'awesome_reports_check_for_updates_daily_schedule');
register_deactivation_hook(__FILE__, 'awesome_reports_check_for_updates_daily_schedule_clear');

/**
 * On plugin deactivation remove the scheduled events.
 */
function awesome_reports_check_for_updates_daily_schedule_clear() {
    wp_clear_scheduled_hook('awesome_reports_check_for_updates_daily');
}
add_action('upgrader_process_complete', 'awesome_reports_after_update', 10, 2);

/**
 * After an update has run, check and log in database.
 */
function awesome_reports_after_update($upgrader_object, $options) {
    if ($options['action'] == 'update') {
        awesome_reports_check_for_updates();
    }
}
add_action('awesome_reports_check_for_updates_daily', 'awesome_reports_check_for_updates');

/**
 * Loop through each type of update and determine if there is now a newer version.
 */
function awesome_reports_check_for_updates() {
    global $wpdb;
    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    if (!function_exists('get_plugins')) {
        require_once ABSPATH . 'wp-admin/includes/plugin.php';
    }

    $timezone  = wp_timezone();
    $now       = new DateTime("now", $timezone);
    $mysqldate = $now->format('Y-m-d');

    $wordpress_version = get_bloginfo('version');

    $last_wp_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'wp' AND `slug` = %s ORDER BY `date` DESC", array('wp')));

    $today_wp_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'wp' AND slug = %s AND date = %s", array(
        'wp',
        $mysqldate
    )));

    if (!$last_wp_update || version_compare($wordpress_version, $last_wp_update->version_after, '>')) {

        $last_version = null;
        if ($last_wp_update) {
            $last_version = $last_wp_update->version_after;
        }

        $update_id = null;
        if ($today_wp_update) {
            $update_id = $today_wp_update->id;
        }

        $wp_update = array(
            'id'             => $update_id,
            'date'           => $mysqldate,
            'type'           => 'wp',
            'name'           => 'WordPress',
            'slug'           => 'wp',
            'version_before' => $last_version,
            'version_after'  => $wordpress_version,
            'active'         => null,
        );

        awesome_reports_track_update($wp_update);
    }

    $themes = wp_get_themes();

    foreach ($themes as $theme_slug => $theme) {

        $theme_active = false;
        $active_theme = get_option('stylesheet');

        if ($theme_slug == $active_theme) {
            $theme_active = true;
        }

        $last_theme_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'theme' AND `slug` = %s ORDER BY `date` DESC", array($theme_slug)));

        $today_theme_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'theme' AND slug = %s AND date = %s", array(
            $theme_slug,
            $mysqldate
        )));

        if (! $last_theme_update || version_compare($theme->get('Version'), $last_theme_update->version_after, '>')) {

            $last_version = null;
            if ($last_theme_update) {
                $last_version = $last_theme_update->version_after;
            }

            $update_id = null;
            if ($today_theme_update) {
                $update_id = $today_theme_update->id;
            }

            $theme_update = array(
                'id'             => $update_id,
                'date'           => $mysqldate,
                'type'           => 'theme',
                'name'           => $theme['Name'],
                'slug'           => $theme_slug,
                'version_before' => $last_version,
                'version_after'  => $theme['Version'],
                'active'         => $theme_active,
            );

            awesome_reports_track_update($theme_update);
        }
    }

    $plugins = get_plugins();

    foreach ($plugins as $plugin_slug => $plugin) {

        $plugin_active = false;
        if (is_plugin_active($plugin_slug)) {
            $plugin_active = true;
        }

        $last_plugin_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'plugin' AND `slug` = %s ORDER BY `date` DESC", array($plugin_slug)));

        $today_plugin_update = $wpdb->get_row($wpdb->prepare("SELECT * FROM $awesome_reports_table_name WHERE `type` = 'plugin' AND `slug` = %s AND `date` = %s", array(
            $plugin_slug,
            $mysqldate
        )));

        if (! $last_plugin_update || version_compare($plugin['Version'], $last_plugin_update->version_after, '>')) {

            $last_version = null;
            if ($last_plugin_update) {
                $last_version = $last_plugin_update->version_after;
            }

            $update_id = null;
            if ($today_plugin_update) {
                $update_id = $today_plugin_update->id;
            }

            $plugin_update = array(
                'id'             => $update_id,
                'date'           => $mysqldate,
                'type'           => 'plugin',
                'name'           => $plugin['Name'],
                'slug'           => $plugin_slug,
                'version_before' => $last_version,
                'version_after'  => $plugin['Version'],
                'active'         => $plugin_active,
            );

            awesome_reports_track_update($plugin_update);
        }
    }

    do_action('awesome_reports_check');
}

/**
 * Track a single update and add it to the database.
 */
function awesome_reports_track_update($thing_to_track) {

    global $wpdb;
    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    $new_entry = $wpdb->replace(
        $awesome_reports_table_name,
        $thing_to_track,
        array(
            '%d',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%s',
            '%d',
        )
    );

    return $new_entry;
}

/**
 * Add the top-level menu page.
 */
function awesome_reports_add_admin_menu() {
    add_menu_page(
        __('Reports', 'awesome-reports'),     // Page title
        __('Reports', 'awesome-reports'),     // Menu title
        'manage_options',                     // Capability
        'awesome_reports',                    // Menu slug
        'awesome_reports_stats_page',         // Function
        'dashicons-chart-bar',                // Icon
        2                                     // Position
    );
}
add_action('admin_menu', 'awesome_reports_add_admin_menu');

/**
 * Enqueue admin styles for the reports page.
 */
function awesome_reports_enqueue_admin_styles($hook) {
    // Only load on our reports page
    if ($hook !== 'toplevel_page_awesome_reports') {
        return;
    }

    wp_enqueue_style(
        'awesome-reports-admin',
        plugin_dir_url(__FILE__) . 'assets/css/admin.css',
        array(),
        AWESOME_REPORTS_VERSION
    );
}
add_action('admin_enqueue_scripts', 'awesome_reports_enqueue_admin_styles');

/**
 * Main Awesome Reports page.
 */
function awesome_reports_stats_page() {
    global $wpdb;
    $awesome_reports_table_name = $wpdb->prefix . 'awesome_reports';

    // Get records from last 30 days
    $thirty_days_ago = date('Y-m-d', strtotime('-30 days'));

    $wp_updates = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $awesome_reports_table_name WHERE type = 'wp' AND date >= %s AND version_before IS NOT NULL AND version_before != '' ORDER BY date DESC",
        $thirty_days_ago
    ));

    $theme_updates = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $awesome_reports_table_name WHERE type = 'theme' AND date >= %s AND version_before IS NOT NULL AND version_before != '' ORDER BY date DESC",
        $thirty_days_ago
    ));

    $plugin_updates = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $awesome_reports_table_name WHERE type = 'plugin' AND date >= %s AND version_before IS NOT NULL AND version_before != '' ORDER BY date DESC",
        $thirty_days_ago
    ));

    // Count updates for each category
    $wp_count = count($wp_updates);
    $theme_count = count($theme_updates);
    $plugin_count = count($plugin_updates);
    $server_count = 3; // Will be implemented later

    // Generate pseudo comparison percentages (only positive ones for demo)
    $wp_comparison = 1;
    $theme_comparison = 1;
    $plugin_comparison = 4;
?>

    <div class="wrap">
        <h1><?php _e('Awesome Reports', 'awesome-reports'); ?></h1>

        <!-- Software Updates Grid -->
        <h2><?php _e('Software Updates', 'awesome-reports'); ?></h2>
        <div class="awesome-reports-grid">
            <!-- WordPress Core Card -->
            <div class="awesome-reports-update-card">
                <h3>
                    <div class="dashicons dashicons-wordpress"></div>
                    <?php _e('WordPress Core', 'awesome-reports'); ?>
                </h3>
                <div class="count">
                    <?php echo esc_html($wp_count); ?>
                    <?php if ($wp_comparison > 0) : ?>
                        <div class="count-comparison">
                            <div class="dashicons dashicons-arrow-up"></div>
                            <?php echo esc_html($wp_comparison); ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="updates-list">
                    <?php if (!empty($wp_updates)) : ?>
                        <?php foreach (array_slice($wp_updates, 0, 5) as $update) : ?>
                            <div class="update-item">
                                <div class="update-date"><?php echo esc_html(date('M j', strtotime($update->date))); ?></div>
                                <div class="update-name"><?php echo esc_html($update->name); ?></div>
                                <div class="update-version">
                                    <?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <?php if (count($wp_updates) > 5) : ?>
                            <div style="color: #646970; font-style: italic;">
                                <?php printf(__('+ %d more updates', 'awesome-reports'), count($wp_updates) - 5); ?>
                            </div>
                        <?php endif; ?>
                    <?php else : ?>
                        <div style="color: #646970; font-style: italic;">
                            <?php _e('No updates in last 30 days', 'awesome-reports'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Themes Card -->
            <div class="awesome-reports-update-card">
                <h3>
                    <div class="dashicons dashicons-admin-appearance"></div>
                    <?php _e('Themes', 'awesome-reports'); ?>
                </h3>
                <div class="count">
                    <?php echo esc_html($theme_count); ?>
                    <?php if ($theme_comparison > 0) : ?>
                        <div class="count-comparison">
                            <div class="dashicons dashicons-arrow-up"></div>
                            <?php echo esc_html($theme_comparison); ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="updates-list">
                    <?php if (!empty($theme_updates)) : ?>
                        <?php foreach (array_slice($theme_updates, 0, 5) as $update) : ?>
                            <div class="update-item">
                                <div class="update-date"><?php echo esc_html(date('M j', strtotime($update->date))); ?></div>
                                <div class="update-name"><?php echo esc_html($update->name); ?></div>
                                <div class="update-version">
                                    <?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <?php if (count($theme_updates) > 5) : ?>
                            <div style="color: #646970; font-style: italic;">
                                <?php printf(__('+ %d more updates', 'awesome-reports'), count($theme_updates) - 5); ?>
                            </div>
                        <?php endif; ?>
                    <?php else : ?>
                        <div style="color: #646970; font-style: italic;">
                            <?php _e('No updates in last 30 days', 'awesome-reports'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Plugins Card -->
            <div class="awesome-reports-update-card">
                <h3>
                    <div class="dashicons dashicons-admin-plugins"></div>
                    <?php _e('Plugins', 'awesome-reports'); ?>
                </h3>
                <div class="count">
                    <?php echo esc_html($plugin_count); ?>
                    <?php if ($plugin_comparison > 0) : ?>
                        <div class="count-comparison">
                            <div class="dashicons dashicons-arrow-up"></div>
                            <?php echo esc_html($plugin_comparison); ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="updates-list">
                    <?php if (!empty($plugin_updates)) : ?>
                        <?php foreach (array_slice($plugin_updates, 0, 5) as $update) : ?>
                            <div class="update-item">
                                <div class="update-date"><?php echo esc_html(date('M j', strtotime($update->date))); ?></div>
                                <div class="update-name"><?php echo esc_html($update->name); ?></div>
                                <div class="update-version">
                                    <?php echo esc_html($update->version_before); ?> → <?php echo esc_html($update->version_after); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <?php if (count($plugin_updates) > 5) : ?>
                            <div style="color: #646970; font-style: italic;">
                                <?php printf(__('+ %d more updates', 'awesome-reports'), count($plugin_updates) - 5); ?>
                            </div>
                        <?php endif; ?>
                    <?php else : ?>
                        <div style="color: #646970; font-style: italic;">
                            <?php _e('No updates in last 30 days', 'awesome-reports'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Server Card -->
            <div class="awesome-reports-update-card">
                <h3>
                    <div class="dashicons dashicons-database"></div>
                    <?php _e('Server', 'awesome-reports'); ?>
                </h3>
                <div class="count"><?php echo esc_html($server_count); ?></div>
                <div class="updates-list">
                    <div class="update-item">
                        <div class="update-date">Jan 1</div>
                        <div class="update-name">PHP</div>
                        <div class="update-version">8.1.12</div>
                    </div>
                    <div class="update-item">
                        <div class="update-date">Jan 1</div>
                        <div class="update-name">Nginx</div>
                        <div class="update-version">1.23.3</div>
                    </div>
                    <div class="update-item">
                        <div class="update-date">Jan 1</div>
                        <div class="update-name">MySQL</div>
                        <div class="update-version">8.0.31</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
<?php
}